{"name": "my-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.0.14", "autoprefixer": "^10.4.20", "axios": "^1.8.2", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "dotenv": "^16.4.7", "postcss": "^8.5.3", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.3.0", "react-toastify": "^11.0.5", "tailwindcss": "^4.0.14"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}
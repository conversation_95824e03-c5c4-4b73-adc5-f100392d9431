# Satya Admin Dashboard

A modern admin dashboard built with <PERSON><PERSON>, Vite, and <PERSON>tra<PERSON>, featuring a clean and responsive design.

## 🚀 Features

- ⚡️ Built with React 19 and Vite 6
- 🎨 Modern UI with Bootstrap 5 and Tailwind CSS
- 🔄 Fast Refresh for quick development
- 📱 Fully responsive design
- 🔒 Secure authentication system
- 📊 Interactive data visualization
- 🔔 Toast notifications
- 🎯 TypeScript support

## 🛠️ Tech Stack

- React 19
- Vite 6
- Bootstrap 5
- Tailwind CSS
- React Router DOM
- Axios
- React Toastify
- TypeScript

## 📦 Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/satya-admin-dashboard.git
cd satya-admin-dashboard
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory and add your environment variables:
```env
VITE_API_URL=your_api_url_here
```

4. Start the development server:
```bash
npm run dev
```

## 🏗️ Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 📁 Project Structure

```
satya-admin-dashboard/
├── src/
│   ├── components/     # Reusable components
│   ├── pages/         # Page components
│   ├── services/      # API services
│   ├── utils/         # Utility functions
│   ├── hooks/         # Custom hooks
│   ├── context/       # React context
│   └── assets/        # Static assets
├── public/            # Public assets
└── ...
```


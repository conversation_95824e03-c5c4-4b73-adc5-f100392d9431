const transformImageUrl = (url) => {
  if (!url) return '';
  const imgUrlParts = url.split("uploads");
  const transformedUrl = imgUrlParts[0] + "public/uploads" + imgUrlParts[1];
  return transformedUrl.replace("http:", "https:");
};

const ensureHttps = (url) => {
  if (!url) return '';
  return url.replace("http:", "https:");
};

const isImageUrl = (url) => {
  if (!url) return false;
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
  return imageExtensions.some(ext => url.toLowerCase().endsWith(ext));
};

export const transformUrl = (url) => {
  if (!url) return '';
  const httpsUrl = ensureHttps(url);
  if (isImageUrl(httpsUrl)) {
    return transformImageUrl(httpsUrl);
  }
  return httpsUrl;
}; 
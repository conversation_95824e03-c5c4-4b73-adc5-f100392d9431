import { createContext, useState, useEffect } from "react";
import axios from "axios";

const AuthContext = createContext();
const BASE_URL = import.meta.env.VITE_API_URL;
const API_CATEGORY = import.meta.env.VITE_API_CATEGORY;

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(localStorage.getItem("userId") || null);
  const [token, setToken] = useState(localStorage.getItem("token") || null);
  const [category, setCategory] = useState(localStorage.getItem("category") || API_CATEGORY);
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const savedMode = localStorage.getItem("darkMode");
    return savedMode ? JSON.parse(savedMode) : window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  // Function to set tokens, user, and category in state and localStorage
  const setAuthData = (userId, token, category) => {
    setUser(userId);
    setToken(token);
    setCategory(category);
    localStorage.setItem("userId", userId);
    localStorage.setItem("token", token);
    localStorage.setItem("category", category);
  };

  // Function to clear auth data
  const clearAuthData = () => {
    setUser(null);
    setToken(null);
    setCategory(API_CATEGORY);
    localStorage.removeItem("userId");
    localStorage.removeItem("token");
    localStorage.removeItem("category");
    localStorage.removeItem("requestId");
  };

  // Toggle dark mode
  const toggleDarkMode = () => {
    setIsDarkMode(prev => {
      const newMode = !prev;
      localStorage.setItem("darkMode", JSON.stringify(newMode));
      return newMode;
    });
  };

  // Login Function (Step 1)
  const login = async (mobileNumber) => {
    try {
      const response = await axios.post(
        `${BASE_URL}/api/admin/login`,
        { mobileNumber },
        {
          headers: {
            category: API_CATEGORY,
          },
        }
      );

      if (response.data.status === "success") {
        const { requestId, userId } = response.data.data;
        localStorage.setItem("requestId", requestId);
        localStorage.setItem("userId", userId);
        setUser(userId);
        return requestId;
      }
    } catch (error) {
      console.error("Login Error:", error.response?.data || error);
      throw error;
    }
  };

  // Verify OTP (Step 2)
  const verifyOtp = async (otp) => {
    const requestId = localStorage.getItem("requestId");
    const userId = localStorage.getItem("userId");

    if (!requestId || !userId) {
      console.error("No requestId or userId found in localStorage.");
      return false;
    }

    try {
      const response = await axios.post(`${BASE_URL}/api/user/login/verify`, { requestId, userId, otp });

      if (response.data.status === "success") {
        const newToken = response.data.data.token;
        setAuthData(userId, newToken, API_CATEGORY); // Save category along with token and userId
        return true; // OTP verification successful
      }
    } catch (error) {
      console.error("OTP Verification Error:", error.response?.data || error);
      clearAuthData(); // If OTP fails, clear stored data
      throw error;
    }
    return false;
  };

  // Refresh Token
  const refreshToken = async () => {
    const storedToken = localStorage.getItem("token");
    if (!storedToken) return;

    try {
      const response = await axios.post(`${BASE_URL}/api/user/refreshToken`, { token: storedToken });

      if (response.data.status === "success") {
        const newToken = response.data.data.token;
        setAuthData(user, newToken, category); // Pass category to setAuthData
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
      clearAuthData();
    }
  };

  // Automatically refresh token every 2.5 hours
  useEffect(() => {
    const interval = setInterval(() => {
      if (token) {
        refreshToken();
      }
    }, 2.5 * 60 * 60 * 1000); // 2.5 hours in milliseconds

    return () => clearInterval(interval);
  }, [token]);

  // Logout
  const logout = () => {
    clearAuthData();
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      token, 
      category, 
      login, 
      verifyOtp, 
      logout,
      isDarkMode,
      toggleDarkMode 
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
import React from 'react';

const StatsCard = ({ title, value, type, isDarkMode }) => {
  const getIcon = () => {
    switch (type) {
      case 'total':
        return (
          <div className={`p-3 rounded-full ${isDarkMode ? 'bg-blue-900/30' : 'bg-blue-100'}`}>
            <svg className={`w-8 h-8 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
        );
      case 'approved':
        return (
          <div className={`p-3 rounded-full ${isDarkMode ? 'bg-green-900/30' : 'bg-green-100'}`}>
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      case 'pending':
        return (
          <div className={`p-3 rounded-full ${isDarkMode ? 'bg-yellow-900/30' : 'bg-yellow-100'}`}>
            <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      case 'rejected':
        return (
          <div className={`p-3 rounded-full ${isDarkMode ? 'bg-red-900/30' : 'bg-red-100'}`}>
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      default:
        return null;
    }
  };

  const getBrandText = () => {
    return type === 'total' ? 'Satya Samadhan' : '';
  };

  const getSubText = () => {
    const category = localStorage.getItem('category') || 'police';
    return type === 'total' ? `${category.charAt(0).toUpperCase() + category.slice(1)} Dashboard` : '';
  };

  return (
    <div className={`relative overflow-hidden p-6 rounded-xl ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm 
      transform transition-all duration-300 hover:scale-105 hover:shadow-lg
      border ${isDarkMode ? 'border-gray-700' : 'border-gray-100'}`}>
      {/* Background Gradient */}
      <div className={`absolute inset-0 opacity-5 ${
        type === 'total' ? 'bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600' :
        type === 'approved' ? 'bg-gradient-to-br from-green-600 to-emerald-600' :
        type === 'pending' ? 'bg-gradient-to-br from-yellow-600 to-amber-600' :
        'bg-gradient-to-br from-red-600 to-rose-600'
      }`}></div>

      {/* Content */}
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>
              {title}
            </h3>
            {getBrandText() && (
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {getBrandText()}
              </p>
            )}
            {getSubText() && (
              <p className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                {getSubText()}
              </p>
            )}
          </div>
          <div className="animate-bounce-slow">
            {getIcon()}
          </div>
        </div>
        
        <div className="flex items-end justify-between">
          <p className={`text-3xl font-bold ${
            type === 'total' ? (isDarkMode ? 'text-blue-400' : 'text-blue-600') :
            type === 'approved' ? 'text-green-600' :
            type === 'pending' ? 'text-yellow-600' :
            type === 'rejected' ? 'text-red-600' :
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            {value}
          </p>
          {type === 'total' && (
            <div className={`text-xs font-medium px-2.5 py-1 rounded-full ${
              isDarkMode ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-800'
            }`}>
              Total Count
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatsCard; 
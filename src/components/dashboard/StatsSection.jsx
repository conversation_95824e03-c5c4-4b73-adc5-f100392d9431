import React from 'react';
import StatsCard from '../StatsCard';

const StatsSection = ({ stats, isDarkMode }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 mt-6">
      <StatsCard
        title="Total Users"
        value={stats.total}
        type="total"
        isDarkMode={isDarkMode}
      />
      <StatsCard
        title="Approved"
        value={stats.approved}
        type="approved"
        isDarkMode={isDarkMode}
      />
      <StatsCard
        title="Pending"
        value={stats.pending}
        type="pending"
        isDarkMode={isDarkMode}
      />
      <StatsCard
        title="Rejected"
        value={stats.rejected}
        type="rejected"
        isDarkMode={isDarkMode}
      />
    </div>
  );
};

export default StatsSection; 
import React from 'react';
import { FaTimes } from 'react-icons/fa';

const RolesCell = ({ roles, userId, onRemoveRole, isDarkMode }) => {
  return (
    <div className="flex flex-wrap gap-2">
      {roles.map((role) => (
        <span 
          key={role} 
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            isDarkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'
          }`}
        >
          {role}
          <button
            onClick={() => onRemoveRole(userId, role)}
            className="ml-1 hover:text-red-500 transition-colors"
          >
            <FaTimes className="w-3 h-3" />
          </button>
        </span>
      ))}
      {roles.length === 0 && (
        <span className="text-gray-400 italic text-sm">No roles assigned</span>
      )}
    </div>
  );
};

export default RolesCell; 
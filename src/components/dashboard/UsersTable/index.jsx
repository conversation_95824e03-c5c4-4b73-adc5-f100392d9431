import React from 'react';
import TableHeader from './TableHeader';
import TableRow from './TableRow';

const UsersTable = ({ 
  users, 
  loading, 
  isDarkMode, 
  onUpdateStatus, 
  onUpdateRole, 
  onRemoveRole,
  onUserSelect,
  onSort 
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className={`ml-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
          Loading users...
        </span>
      </div>
    );
  }

  return (
    <div className={`rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
      <div className="overflow-x-auto">
        <table className="w-full">
          <TableHeader isDarkMode={isDarkMode} onSort={onSort} />
          <tbody className={`divide-y ${isDarkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
            {users.map((user) => (
              <TableRow
                key={user._id}
                user={user}
                isDarkMode={isDarkMode}
                onUpdateStatus={onUpdateStatus}
                onUpdateRole={onUpdateRole}
                onRemoveRole={onRemoveRole}
                onSelect={onUserSelect}
              />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default UsersTable; 
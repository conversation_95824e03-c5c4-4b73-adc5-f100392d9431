import React from 'react';
import StatusSelect from './StatusSelect';
import RolesCell from './RolesCell';

const availableRoles = ["officer", "it-admin", "moderator", "supervisor", "dispatcher"];

const TableRow = ({ user, isDarkMode, onUpdateStatus, onUpdateRole, onRemoveRole, onSelect }) => {
  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <tr 
      className={`${isDarkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-white hover:bg-gray-50'} cursor-pointer transition-colors`}
      onClick={(e) => {
        if (!e.target.closest('button') && !e.target.closest('select')) {
          onSelect(user);
        }
      }}
    >
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className={`flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center text-sm font-medium ${
            isDarkMode ? 'bg-gray-700 text-white' : 'bg-[#0B36A1] text-white'
          }`}>
            {getInitials(user.name)}
          </div>
          <div className={`ml-4 font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            {user.name}
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={isDarkMode ? 'text-gray-300' : 'text-gray-500'}>
          {user.mobileNumber}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <StatusSelect
          status={user.status}
          onChange={(e) => onUpdateStatus(user._id, e.target.value)}
          isDarkMode={isDarkMode}
        />
      </td>
      <td className="px-6 py-4" onClick={(e) => e.stopPropagation()}>
        <RolesCell
          roles={user.roles}
          userId={user._id}
          onRemoveRole={onRemoveRole}
          isDarkMode={isDarkMode}
        />
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={isDarkMode ? 'text-gray-300' : 'text-gray-500'}>
          {user.policeProfile?.designation || "—"}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={isDarkMode ? 'text-gray-300' : 'text-gray-500'}>
          {user.policeProfile?.department?.name || "—"}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={isDarkMode ? 'text-gray-300' : 'text-gray-500'}>
          {user.policeProfile?.policeStation?.name || "—"}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap" onClick={(e) => e.stopPropagation()}>
        <select
          className={`form-select px-3 py-1 rounded-md text-sm ${
            isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-gray-50 border-gray-300'
          }`}
          onChange={(e) => {
            if (e.target.value) {
              onUpdateRole(user._id, e.target.value);
              e.target.value = "";
            }
          }}
        >
          <option value="">+ Add Role</option>
          {availableRoles.filter(role => !user.roles.includes(role)).map((role) => (
            <option key={role} value={role}>
              {role}
            </option>
          ))}
        </select>
      </td>
    </tr>
  );
};

export default TableRow; 
import React from 'react';
import { FaSort } from 'react-icons/fa';

const TableHeader = ({ isDarkMode, onSort }) => {
  const headers = [
    { key: 'name', label: 'Name' },
    { key: 'mobile', label: 'Mobile' },
    { key: 'status', label: 'Status' },
    { key: 'roles', label: 'Roles' },
    { key: 'designation', label: 'Designation' },
    { key: 'department', label: 'Department' },
    { key: 'station', label: 'Station' },
    { key: 'actions', label: 'Actions' }
  ];

  return (
    <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
      <tr>
        {headers.map((header) => (
          <th 
            key={header.key}
            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:text-gray-700"
            onClick={() => onSort && onSort(header.key)}
          >
            <div className="flex items-center space-x-1">
              <span>{header.label}</span>
              <FaSort className="w-3 h-3" />
            </div>
          </th>
        ))}
      </tr>
    </thead>
  );
};

export default TableHeader; 
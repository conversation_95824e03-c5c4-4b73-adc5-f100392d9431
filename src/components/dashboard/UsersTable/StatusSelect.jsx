import React from 'react';

const statusConfig = {
  approved: { color: "success", icon: "✓", bgColor: "bg-green-100", textColor: "text-green-800" },
  requested: { color: "warning", icon: "⟳", bgColor: "bg-yellow-100", textColor: "text-yellow-800" },
  rejected: { color: "danger", icon: "✕", bgColor: "bg-red-100", textColor: "text-red-800" },
  blocked: { color: "secondary", icon: "⊘", bgColor: "bg-gray-100", textColor: "text-gray-800" },
};

const StatusSelect = ({ status, onChange, isDarkMode }) => (
  <select
    className={`form-select px-3 py-1 rounded-md text-sm ${
      isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-gray-50 border-gray-300'
    }`}
    value={status}
    onChange={onChange}
  >
    {Object.entries(statusConfig).map(([statusKey, config]) => (
      <option key={statusKey} value={statusKey} className={`${config.bgColor} ${config.textColor}`}>
        {config.icon} {statusKey}
      </option>
    ))}
  </select>
);

export default StatusSelect; 
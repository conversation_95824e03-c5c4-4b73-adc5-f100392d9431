import React from 'react';
import { transformUrl } from '../../utils/imageUtils';
import InfoItem from './InfoItem';

const UserDetailsModal = ({ user, userDetails, loading, onClose, isDarkMode }) => {
  if (!user) return null;

  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-black opacity-75 transition-opacity" onClick={onClose}></div>

        <div className={`inline-block align-bottom rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full ${
          isDarkMode ? 'bg-gray-800' : 'bg-white'
        }`}>
          {/* Modal header */}
          <div className={`px-6 py-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <h3 className={`text-2xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                User Details
              </h3>
              <button
                onClick={onClose}
                className={`rounded-full p-1 hover:bg-opacity-75 transition-colors ${
                  isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-500'
                }`}
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Modal content */}
          <div className="px-6 py-4">
            {loading ? (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className={`ml-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Loading details...</span>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Header with Photo and Basic Info */}
                <div className="flex items-start space-x-4">
                  {userDetails?.displayUrl ? (
                    <img 
                      src={transformUrl(userDetails.displayUrl)} 
                      alt={userDetails.name}
                      className="h-24 w-24 rounded-lg object-cover"
                      onError={(e) => {
                        e.target.src = ''; // Clear the broken image
                        e.target.onerror = null; // Prevent infinite loop
                        e.target.className = `h-24 w-24 rounded-lg flex items-center justify-center text-3xl font-medium ${
                          isDarkMode ? 'bg-gray-700 text-white' : 'bg-[#0B36A1] text-white'
                        }`;
                        e.target.innerHTML = getInitials(userDetails?.name || '');
                      }}
                    />
                  ) : (
                    <div className={`h-24 w-24 rounded-lg flex items-center justify-center text-3xl font-medium ${
                      isDarkMode ? 'bg-gray-700 text-white' : 'bg-[#0B36A1] text-white'
                    }`}>
                      {getInitials(userDetails?.name || '')}
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {userDetails?.name}
                    </h3>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {userDetails?.policeProfile?.designation}
                    </p>
                    <div className="mt-2 flex flex-wrap gap-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize
                        ${userDetails?.status === 'approved' ? 'bg-green-100 text-green-800' :
                          userDetails?.status === 'requested' ? 'bg-yellow-100 text-yellow-800' :
                          userDetails?.status === 'rejected' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'}`}>
                        {userDetails?.status}
                      </span>
                      {userDetails?.roles.map((role, index) => (
                        <span
                          key={index}
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            ${isDarkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'}`}>
                          {role}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Details Grid */}
                <div className={`grid grid-cols-3 gap-4 p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <div className="col-span-3 border-b border-gray-600 pb-2 mb-2">
                    <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Contact Information</h4>
                  </div>
                  <InfoItem label="Mobile" value={userDetails?.mobileNumber} isDarkMode={isDarkMode} />
                  <InfoItem label="Email" value={userDetails?.emailId} isDarkMode={isDarkMode} />
                  <InfoItem label="Aadhar" value={userDetails?.aadhar} isDarkMode={isDarkMode} />
                  
                  <div className="col-span-3 border-b border-gray-600 pb-2 mb-2 mt-4">
                    <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Department Details</h4>
                  </div>
                  <InfoItem label="Department" value={userDetails?.policeProfile?.department?.name} isDarkMode={isDarkMode} />
                  <InfoItem label="Department Code" value={userDetails?.policeProfile?.department?.code} isDarkMode={isDarkMode} />
                  <InfoItem label="Designation" value={userDetails?.policeProfile?.designation} isDarkMode={isDarkMode} />
                  
                  <div className="col-span-3 border-b border-gray-600 pb-2 mb-2 mt-4">
                    <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Station Information</h4>
                  </div>
                  <InfoItem label="Station Name" value={userDetails?.policeProfile?.policeStation?.name} isDarkMode={isDarkMode} />
                  <InfoItem label="Station Code" value={userDetails?.policeProfile?.policeStation?.code} isDarkMode={isDarkMode} />
                  <InfoItem label="City" value={userDetails?.policeProfile?.policeStation?.city} isDarkMode={isDarkMode} />
                  <InfoItem label="District" value={userDetails?.policeProfile?.policeStation?.district} isDarkMode={isDarkMode} />
                  <InfoItem label="State" value={userDetails?.policeProfile?.policeStation?.state} isDarkMode={isDarkMode} />
                  <InfoItem label="Pin Code" value={userDetails?.policeProfile?.policeStation?.pinCode} isDarkMode={isDarkMode} />
                </div>

                {/* Timestamps */}
                <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} flex justify-between mt-4`}>
                  <span>Created: {new Date(userDetails?.createdAt).toLocaleDateString()}</span>
                  <span>Last Updated: {new Date(userDetails?.updatedAt).toLocaleDateString()}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDetailsModal; 
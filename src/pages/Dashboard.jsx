import React, { useContext, useEffect, useState } from "react";
import AuthContext from "../context/AuthContext";
import Navbar from "../components/Navbar";
import Sidebar from "../components/Sidebar";
import UserDetailsModal from "../components/dashboard/UserDetailsModal";
import StatsSection from "../components/dashboard/StatsSection";
import FiltersSection from "../components/dashboard/FiltersSection";
import UsersTable from "../components/dashboard/UsersTable";
import { ENDPOINTS } from '../config/apiEndpoints';

const Dashboard = () => {
  const { user, token, isDarkMode } = useContext(AuthContext);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState("");
  const [updateSuccess, setUpdateSuccess] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [userDetails, setUserDetails] = useState(null);
  const [modalLoading, setModalLoading] = useState(false);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const BASE_URL = import.meta.env.VITE_API_URL;
  const API_CATEGORY = import.meta.env.VITE_API_CATEGORY;

  // Fetch users from the API
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const url = statusFilter
        ? ENDPOINTS.USERS.GET_BY_STATUS(API_CATEGORY, statusFilter)
        : ENDPOINTS.USERS.GET_ALL(API_CATEGORY);

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.status === "fail") {
        setUsers([]);
        setError(data.message);
      } else {
        setUsers(data.data);
        setError(null);
      }
    } catch (error) {
      setError("Failed to fetch users. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  // Update user status
  const updateUserStatus = async (userId, status) => {
    try {
      const userToUpdate = users.find((u) => u._id === userId);
      if (!userToUpdate) {
        setError("User not found");
        return;
      }

      const response = await fetch(ENDPOINTS.USERS.UPDATE_STATUS, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId,
          status,
          roles: userToUpdate.roles,
        }),
      });

      const data = await response.json();

      if (data.status === "success") {
        setUpdateSuccess(`Status updated to "${status}" for ${userToUpdate.name}`);
        setTimeout(() => setUpdateSuccess(""), 3000);
        fetchUsers();
      } else {
        setError(data.message || "Failed to update user status.");
      }
    } catch (error) {
      setError("Failed to update user status. Please try again later.");
    }
  };

  // Update user role
  const updateUserRole = async (userId, role) => {
    try {
      const userToUpdate = users.find((u) => u._id === userId);
      if (!userToUpdate) {
        setError("User not found");
        return;
      }

      const response = await fetch(ENDPOINTS.USERS.UPDATE_ROLE, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId,
          status: userToUpdate.status,
          roles: [...userToUpdate.roles, role],
        }),
      });

      const data = await response.json();

      if (data.status === "success") {
        setUpdateSuccess(`Role "${role}" added successfully to ${userToUpdate.name}`);
        setTimeout(() => setUpdateSuccess(""), 3000);
        fetchUsers();
      } else {
        setError(data.message || "Failed to update user role.");
      }
    } catch (error) {
      setError("Failed to update user role. Please try again later.");
    }
  };

  // Remove user role
  const removeUserRole = async (userId, roleToRemove) => {
    try {
      const userToUpdate = users.find((u) => u._id === userId);
      if (!userToUpdate) {
        setError("User not found");
        return;
      }

      const updatedRoles = userToUpdate.roles.filter((role) => role !== roleToRemove);

      const response = await fetch(ENDPOINTS.USERS.UPDATE_ROLE, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId,
          status: userToUpdate.status,
          roles: updatedRoles,
        }),
      });

      const data = await response.json();

      if (data.status === "success") {
        setUpdateSuccess(`Role "${roleToRemove}" removed successfully from ${userToUpdate.name}`);
        setTimeout(() => setUpdateSuccess(""), 3000);
        fetchUsers();
      } else {
        setError(data.message || "Failed to remove user role.");
      }
    } catch (error) {
      setError("Failed to remove user role. Please try again later.");
    }
  };

  // Fetch user details
  const fetchUserDetails = async (userId) => {
    try {
      setModalLoading(true);
      const response = await fetch(ENDPOINTS.USERS.GET_DETAILS(userId), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.status === "success") {
        setUserDetails(data.data);
      } else {
        setError(data.message || "Failed to fetch user details");
      }
    } catch (error) {
      setError("Failed to fetch user details. Please try again later.");
    } finally {
      setModalLoading(false);
    }
  };

  // Handle sorting
  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Filter and sort users
  const filteredAndSortedUsers = React.useMemo(() => {
    let filtered = users.filter(
      (user) =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.mobileNumber.includes(searchTerm) ||
        (user.policeProfile?.designation || "").toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.policeProfile?.department?.name || "").toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.policeProfile?.policeStation?.name || "").toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (sortConfig.key) {
      filtered.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [users, searchTerm, sortConfig]);

  // Calculate stats
  const stats = {
    total: users.length,
    approved: users.filter(user => user.status === 'approved').length,
    pending: users.filter(user => user.status === 'requested').length,
    rejected: users.filter(user => user.status === 'rejected').length
  };

  useEffect(() => {
    fetchUsers();
  }, [token, statusFilter]);

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <Navbar toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} />
      <Sidebar isOpen={isSidebarOpen} isDarkMode={isDarkMode} />

      <main className={`transition-all duration-300 ${isSidebarOpen ? 'ml-64' : 'ml-0'} pt-16 px-4`}>
        <StatsSection stats={stats} isDarkMode={isDarkMode} />
        
        <FiltersSection 
          isDarkMode={isDarkMode}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
        />

        {/* Messages */}
        {updateSuccess && (
          <div className="mb-4 p-4 rounded-lg bg-green-100 text-green-700 border border-green-200">
            {updateSuccess}
          </div>
        )}

        {error && (
          <div className="mb-4 p-4 rounded-lg bg-red-100 text-red-700 border border-red-200">
            {error}
          </div>
        )}

        <UsersTable 
          users={filteredAndSortedUsers}
          loading={loading}
          isDarkMode={isDarkMode}
          onUpdateStatus={updateUserStatus}
          onUpdateRole={updateUserRole}
          onRemoveRole={removeUserRole}
          onUserSelect={(user) => {
            setSelectedUser(user);
            fetchUserDetails(user._id);
          }}
          onSort={handleSort}
        />
      </main>

      {selectedUser && (
        <UserDetailsModal
          user={selectedUser}
          userDetails={userDetails}
          loading={modalLoading}
          onClose={() => {
            setSelectedUser(null);
            setUserDetails(null);
          }}
          isDarkMode={isDarkMode}
        />
      )}
    </div>
  );
};

export default Dashboard;
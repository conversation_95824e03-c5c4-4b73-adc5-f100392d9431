import { useState, useContext } from "react";
import AuthContext from "../context/AuthContext";
import { useNavigate } from "react-router-dom";
import logo from '../assets/satya_samadhan_logo.png';
import backgroundC from '../assets/backgroundC.svg';
import backgroundC2 from '../assets/backgroundC2.svg';

const Login = () => {
  const { login, verifyOtp, category, isDarkMode, toggleDarkMode } = useContext(AuthContext);
  const [mobileNumber, setMobileNumber] = useState("");
  const [requestId, setRequestId] = useState(null);
  const [otp, setOtp] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const handleLogin = async () => {
    if (!mobileNumber) {
      setError("Please enter your mobile number");
      return;
    }
    
    setIsLoading(true);
    setError("");
    
    try {
      const reqId = await login(mobileNumber);
      if (reqId) {
        setRequestId(reqId);
        setError("");
      }
    } catch (error) {
      setError("Login failed. Please try again.");
      console.error("Login failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp) {
      setError("Please enter the OTP");
      return;
    }
    
    setIsLoading(true);
    setError("");
    
    try {
      if (requestId) {
        const isVerified = await verifyOtp(otp);
        if (isVerified) {
          navigate("/dashboard");
        } else {
          setError("Invalid OTP. Please try again.");
        }
      }
    } catch (error) {
      setError("OTP verification failed. Please try again.");
      console.error("OTP verification failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const dashboardType = category === "police" ? "Police Dashboard" : "Forensic Dashboard";

  return (
    <div className={`min-h-screen transition-colors duration-500 ${
      isDarkMode 
        ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900' 
        : 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50'
    } flex items-center justify-center p-4 relative overflow-hidden`}>
      {/* SVG Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <img
          src={backgroundC}
          alt=""
          className={`absolute -top-40 -left-40 w-80 h-80 animate-blob ${
            isDarkMode ? 'opacity-40' : 'opacity-10'
          }`}
        />
        <img
          src={backgroundC2}
          alt=""
          className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 animate-blob animation-delay-4000 ${
            isDarkMode ? 'opacity-40' : 'opacity-10'
          }`}
        />
      </div>

      {/* Theme Toggle */}
      <button
        onClick={toggleDarkMode}
        className={`absolute top-4 right-4 p-3 rounded-full backdrop-blur-sm transition-all duration-300 ${
          isDarkMode 
            ? 'bg-gray-800/50 hover:bg-gray-700/50 text-yellow-300' 
            : 'bg-white/50 hover:bg-white/70 text-gray-800'
        }`}
      >
        {isDarkMode ? (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        ) : (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        )}
      </button>

      <div className="w-full max-w-md relative">
        <div className={`backdrop-blur-xl rounded-3xl shadow-2xl overflow-hidden border ${
          isDarkMode 
            ? 'bg-gray-800/50 border-gray-700' 
            : 'bg-white/50 border-white/20'
        } transition-all duration-500`}>
          {/* Header Section */}
          <div className={`p-8 text-center relative overflow-hidden ${
            isDarkMode ? 'bg-gray-800/50' : 'bg-white/50'
          }`}>
            <div className={`absolute inset-0 bg-gradient-to-r ${
              isDarkMode 
                ? 'from-blue-900/20 to-purple-900/20' 
                : 'from-blue-500/10 to-purple-500/10'
            }`}></div>
            <div className="relative z-10">
              <img 
                src={logo} 
                alt="Satya Samadhan Admin Logo" 
                className="w-24 h-auto mx-auto mb-6 transform hover:scale-105 transition-transform duration-300"
              />
              <h1 className={`text-3xl font-bold mb-3 ${
                isDarkMode ? 'text-white' : 'text-gray-800'
              }`}>Satya Samadhan Admin</h1>
              <p className={`text-sm mb-4 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>Secure Authentication System</p>
              <div className={`inline-block px-4 py-2 rounded-full ${
                isDarkMode 
                  ? 'bg-gray-700/50 text-gray-200' 
                  : 'bg-gray-100/50 text-gray-800'
              }`}>
                <span className="font-medium">{dashboardType}</span>
              </div>
            </div>
          </div>

          {/* Form Section */}
          <div className="p-8">
            {error && (
              <div className={`mb-6 p-4 rounded-xl ${
                isDarkMode 
                  ? 'bg-red-900/50 text-red-200 border border-red-800' 
                  : 'bg-red-50 text-red-600 border border-red-200'
              } animate-fadeIn`}>
                <p className="text-sm">{error}</p>
              </div>
            )}

            {!requestId ? (
              <div className="space-y-6 animate-slideIn">
                <div>
                  <label htmlFor="mobileNumber" className={`block text-sm font-medium mb-2 ${
                    isDarkMode ? 'text-gray-200' : 'text-gray-700'
                  }`}>
                    Mobile Number
                  </label>
                  <input
                    type="text"
                    id="mobileNumber"
                    className={`w-full px-4 py-3 rounded-xl border transition-all duration-300 ${
                      isDarkMode 
                        ? 'bg-gray-700/50 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-blue-500/30' 
                        : 'bg-white/50 border-gray-200 text-gray-800 placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/30'
                    }`}
                    placeholder="Enter your mobile number"
                    value={mobileNumber}
                    onChange={(e) => setMobileNumber(e.target.value)}
                  />
                </div>
                <button
                  onClick={handleLogin}
                  disabled={isLoading}
                  className={`w-full py-3 rounded-xl font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98] ${
                    isDarkMode
                      ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 focus:ring-blue-500/30'
                      : 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:from-blue-600 hover:to-indigo-600 focus:ring-blue-500/30'
                  }`}
                >
                  {isLoading ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending OTP...
                    </span>
                  ) : (
                    "Send OTP"
                  )}
                </button>
              </div>
            ) : (
              <div className="space-y-6 animate-slideIn">
                <div>
                  <label htmlFor="otp" className={`block text-sm font-medium mb-2 ${
                    isDarkMode ? 'text-gray-200' : 'text-gray-700'
                  }`}>
                    One-Time Password (OTP)
                  </label>
                  <input
                    type="text"
                    id="otp"
                    className={`w-full px-4 py-3 rounded-xl border transition-all duration-300 ${
                      isDarkMode 
                        ? 'bg-gray-700/50 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-blue-500/30' 
                        : 'bg-white/50 border-gray-200 text-gray-800 placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/30'
                    }`}
                    placeholder="Enter OTP sent to your mobile"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                  />
                </div>
                <div className="space-y-3">
                  <button
                    onClick={handleVerifyOtp}
                    disabled={isLoading}
                    className={`w-full py-3 rounded-xl font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98] ${
                      isDarkMode
                        ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 focus:ring-blue-500/30'
                        : 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:from-blue-600 hover:to-indigo-600 focus:ring-blue-500/30'
                    }`}
                  >
                    {isLoading ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Verifying...
                      </span>
                    ) : (
                      "Verify OTP"
                    )}
                  </button>
                  <button
                    onClick={() => setRequestId(null)}
                    disabled={isLoading}
                    className={`w-full py-3 rounded-xl font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98] ${
                      isDarkMode 
                        ? 'bg-gray-700/50 text-gray-200 border border-gray-600 hover:bg-gray-600/50 focus:ring-gray-500' 
                        : 'bg-white/50 text-gray-800 border border-gray-200 hover:bg-gray-50 focus:ring-gray-500'
                    }`}
                  >
                    Back to Login
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className={`px-8 py-4 border-t ${
            isDarkMode 
              ? 'bg-gray-800/50 border-gray-700' 
              : 'bg-white/50 border-gray-100'
          }`}>
            <p className={`text-center text-sm ${
              isDarkMode ? 'text-gray-400' : 'text-gray-500'
            }`}>
              © 2024 Satya Samadhan Admin. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
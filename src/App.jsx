import { useContext, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from "react-router-dom";
import AuthContext from "./context/AuthContext";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";



function App() {
  const { token, user } = useContext(AuthContext);
  const navigate = useNavigate();


  useEffect(() => {
    if (!token) {
      navigate("/login");
    } else {
      navigate("/dashboard");
    }
  }, [token, navigate]);

  return (
    <Routes>
      {/* Public Route - Redirect to Dashboard if already logged in */}
      <Route path="/login" element={!token ? <Login /> : <Navigate to="/dashboard" />} />

      {/* Protected Route - Redirect to Login if no token */}
      <Route path="/dashboard" element={token ? <Dashboard /> : <Navigate to="/login" />} />

      {/* Default Route - Redirect based on token */}
      <Route path="*" element={<Navigate to={token ? "/dashboard" : "/login"} />} />
    </Routes>
  );
}

export default function AppWrapper() {
  return (
    <Router>
      <App />
    </Router>
  );
}